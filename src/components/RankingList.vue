<template>
  <div class="ranking-list">
    <div class="ranking-header">
      <h3 class="ranking-title">{{ title }}</h3>
    </div>
    <ul>
      <li v-for="(item, index) in data" :key="index" :class="['ranking-item', 'rank-' + (index + 1)]">
        <span class="name">{{ item.name }}</span>
        <span class="value">{{ item.value }}</span>
        <span class="rank">{{ index + 1 }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'RankingList',
  props: {
    title: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      required: true
    }
  }
};
</script>

<style scoped>
.ranking-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ranking-title {
  font-size: 16px;
  color: #aae9ff;
  margin: 0;
  padding-left: 10px;
  border-left: 3px solid #00eaff;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  border-bottom: 1px solid #1a3a8b;
}

.ranking-item:last-child {
  border-bottom: none;
}

.name {
  flex-grow: 1;
  padding: 0 10px;
  color: #aae9ff;
}

.value {
  color: #00eaff;
  font-weight: bold;
  margin-right: 10px;
}

.rank {
  width: 30px;
  text-align: center;
  font-weight: bold;
  color: #fff;
  background-color: rgba(0, 234, 255, 0.2);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
}

/* Top 3 styles */
.rank-1 .rank {
  background-color: rgba(255, 215, 0, 0.3);
  color: #ffd700;
}

.rank-2 .rank {
  background-color: rgba(192, 192, 192, 0.3);
  color: #c0c0c0;
}

.rank-3 .rank {
  background-color: rgba(205, 127, 50, 0.3);
  color: #cd7f32;
}

.rank-1 {
  background-color: rgba(255, 215, 0, 0.1);
}

.rank-2 {
  background-color: rgba(192, 192, 192, 0.1);
}

.rank-3 {
  background-color: rgba(205, 127, 50, 0.1);
}
</style>