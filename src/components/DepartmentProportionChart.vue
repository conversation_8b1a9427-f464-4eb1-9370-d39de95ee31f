<template>
  <div class="chart-container">
    <div class="chart-header">
      <h3 class="chart-title">各部门时长占比</h3>
    </div>
    <v-chart class="chart" :option="option" />
    <div class="legend">
      <div v-for="item in legendData" :key="item.name" class="legend-item">
        <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
        <span class="legend-name">{{ item.name }}</span>
        <span class="legend-value">({{ item.value }} {{ item.percentage }})</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DepartmentProportionChart',
  data() {
    return {
      option: {},
      legendData: []
    };
  },
  mounted() {
    // 组件挂载后加载初始数据
    this.loadData();
  },
  methods: {
    // 颜色变亮函数，用于创建3D渐变效果
    lightenColor(color, amount) {
      const usePound = color[0] === '#';
      const col = usePound ? color.slice(1) : color;
      const num = parseInt(col, 16);
      let r = (num >> 16) + amount * 255;
      let g = (num >> 8 & 0x00FF) + amount * 255;
      let b = (num & 0x0000FF) + amount * 255;
      r = r > 255 ? 255 : r < 0 ? 0 : r;
      g = g > 255 ? 255 : g < 0 ? 0 : g;
      b = b > 255 ? 255 : b < 0 ? 0 : b;
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    },

    loadData() {
      // 模拟数据
      const data = [
        { value: 110, name: '机械部门', percentage: '30%' },
        { value: 160, name: '电气部门', percentage: '50%' },
        { value: 90, name: '软件部门', percentage: '20%' }
      ];
      this.processData(data);
    },
    processData(data) {
      const total = 360;
      const colors = ['#d8c7a9', '#34c8e8', '#2d8cf0'];

      this.legendData = data.map((item, index) => ({
        name: item.name,
        value: item.value,
        percentage: item.percentage,
        color: colors[index]
      }));

      this.option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: '时长占比',
            type: 'pie',
            radius: ['50%', '85%'],
            center: ['50%', '55%'],
            avoidLabelOverlap: false,
            // 3D效果配置
            itemStyle: {
              borderRadius: 8,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            label: {
              show: true,
              position: 'center',
              formatter: () => `${total}(H)`,
              fontSize: 16,
              fontWeight: 'bold',
              color: '#fff',
              textShadowColor: 'rgba(0, 0, 0, 0.8)',
              textShadowBlur: 3,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 20,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.8)'
              }
            },
            labelLine: {
              show: false
            },
            data: data.map((item, index) => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: this.lightenColor(colors[index], 0.3)
                    },
                    {
                      offset: 1,
                      color: colors[index]
                    }
                  ]
                },
                borderRadius: 8,
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowOffsetX: 2,
                shadowOffsetY: 2
              }
            }))
          }
        ]
      };
    }
  }
};
        </script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 21, 41, 0.7);
  border-radius: 4px;
  padding: 15px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.chart-title {
  font-size: 16px;
  color: #aae9ff;
  margin-bottom: 10px;
  padding-left: 10px;
  border-left: 3px solid #00eaff;
}

.chart {
  flex: 1;
  min-height: 0;
  max-height: 60%;
  width: 100%;
}

.legend {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  flex-shrink: 0;
  max-height: 35%;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fff;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-name {
  font-size: 12px;
}

.legend-value {
  font-size: 12px;
  color: #a9b7c6;
}
</style>
